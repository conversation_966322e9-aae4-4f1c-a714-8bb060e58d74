/**
 * Control Tab Script for POC Streaming
 *
 * This script is injected into the control tab to manage WebRTC connections
 * and coordinate streaming between target tabs and web clients
 */

import InterceptorPipeline from '../interceptors/interceptor-pipeline';
import { InterceptorRegistry } from '../interceptors/interceptor-registry';
import type { InterceptorPipelineInterface } from '../types';
import CDPManager from './cdp-manager';

// Type definitions for WebRTC and streaming
interface WebClientGroup {
  RTCPeerConnection: RTCPeerConnection | null;
  interceptorPipeline: InterceptorPipelineInterface | null;
  dataChannel: RTCDataChannel | null;
  webClient: {
    clientInfo: any;
    purpose: string;
    currentTabId: string | null;
    connected: boolean;
  };
}

interface TabGroup {
  tabInfo: any;
  stream: MediaStream | null;
}

interface InterceptorConfigSet {
  interceptorNames: string[];
  configs: Record<string, any>;
}

interface StreamInfo {
  targetTabId: string;
  status: string;
}

interface UserEvent {
  type: string;
  eventType?: string;
  [key: string]: any;
}

class ControlTabManager {
  // Connection properties
  private signalingServerUrl: string;
  private wsBrowserEndpoint: string;
  private websocket: WebSocket | null;
  private isConnected: boolean;
  private rtcConfig: RTCConfiguration;
  public interceptorRegistry: InterceptorRegistry;
  // Client and tab management
  private webClientGroups: Map<string, WebClientGroup>;
  private tabGroups: Map<string, TabGroup>;
  private targetConnections: Map<string, RTCPeerConnection>;

  // CDP Manager
  private cdpManager: CDPManager | null;
  private clientId?: string;

  constructor() {
    this.signalingServerUrl = '${KAKU_WS_ENDPOINT}'; // Will be set dynamically
    this.wsBrowserEndpoint = '${BROWSER_WS_ENDPOINT}'; // Will be set dynamically
    this.websocket = null;
    this.isConnected = false;

    // WebRTC configuration
    this.rtcConfig = {
      iceServers: [
        { urls: 'stun:stun.cloudflare.com:3478' },
        { urls: 'stun:stun.l.google.com:19302' },
      ],
      iceCandidatePoolSize: 10,
    };

    // Connection management - refactored for multi-client support with grouped structures

    // Grouped WebClient management structure
    this.webClientGroups = new Map(); // webClientId -> { RTCPeerConnection, interceptorPipeline, dataChannel, webClient }

    // Grouped Tab management structure
    this.tabGroups = new Map(); // tabId -> { tabInfo, stream }

    // Legacy connections for target tabs (unchanged)
    this.targetConnections = new Map(); // tabId -> RTCPeerConnection (to target tab)

    this.interceptorRegistry = new InterceptorRegistry();

    // CDP connection management using CDPManager
    this.cdpManager = null;

    this.init();
  }

  async init() {
    console.log('[control-tab-manager] Initializing control tab manager...');

    this.initializeCDPManager();
    await this.connectToSignalingServer();

    this.setupPageListeners();
    this.createControlTabUI();
  }

  initializeCDPManager() {
    try {
      console.log('[control-tab-manager] Checking for CDPManager availability...');

      console.log('[control-tab-manager] CDPManager found, initializing...');
      this.cdpManager = new CDPManager({
        wsEndpoint: this.wsBrowserEndpoint,
        debug: true,
      });

      this.cdpManager?.initializeDefaultHandlers();

      console.log('[control-tab-manager] CDP Manager initialized successfully');
      console.log('[control-tab-manager] CDP Manager instance:', this.cdpManager);
    } catch (error) {
      console.error('[control-tab-manager] Failed to initialize CDP Manager:', error);
      throw error;
    }
  }

  /**
   * Initialize interceptors for a specific client (supports multiple interceptors)
   */
  initializePipeline(webClientId: string): InterceptorPipelineInterface {
    const webClientGroup = this.webClientGroups.get(webClientId);

    if (!webClientGroup) {
      console.warn('[control-tab-manager] No web client group found for:', webClientId);
      throw new Error('No web client group found for ' + webClientId);
    }

    try {
      // Create interceptor instances
      const interceptors = this.interceptorRegistry.createClientInterceptors(webClientId);

      // Create pipeline with interceptors
      const pipeline = new InterceptorPipeline(interceptors);

      // Store pipeline in the grouped structure
      webClientGroup.interceptorPipeline = pipeline;

      // Update debug panel
      setTimeout(() => this.updateInterceptorDebugPanel(), 100);
      return pipeline;
    } catch (error) {
      console.error(
        '[control-tab-manager] Error initializing interceptors for client:',
        webClientId,
        error,
      );
      throw error;
    }
  }

  /**
   * Process incoming stream through client-specific interceptor pipeline
   */
  async processStreamForClient(stream: MediaStream, webClientId: string): Promise<MediaStream> {
    const webClientGroup = this.webClientGroups.get(webClientId);
    const config = this.interceptorRegistry.getClientConfiguration(webClientId);
    if (!webClientGroup) {
      console.warn('Cannot process stream - No webClientGroup found for:', webClientId);
      throw new Error('No webClientGroup found for ' + webClientId);
    }
    if (!config) {
      return stream; // No interceptors configured
    }

    // Check if interceptors are enabled for this client
    if (config.interceptorNames.length === 0) {
      return stream; // No interceptors configured
    }

    try {
      const videoTrack = stream.getVideoTracks()[0];
      if (!videoTrack) return stream;

      // Get or create interceptor pipeline for this client
      let pipeline = webClientGroup.interceptorPipeline;

      if (!pipeline) {
        // Initialize interceptors if not already done
        pipeline = this.initializePipeline(webClientId);
        if (!pipeline) {
          console.warn(
            '[control-tab-manager] Failed to initialize interceptors for client:',
            webClientId,
          );
          return stream;
        }
      }

      console.log(
        '[control-tab-manager] Processing stream through interceptor pipeline for client:',
        webClientId,
        'interceptors:',
        config.interceptorNames,
      );

      // Initialize the pipeline with the video track
      const processedTrack = await pipeline.initialize(videoTrack);

      // Create new stream with processed video track and original audio tracks
      const audioTracks = stream.getAudioTracks();

      // Debug: Log track information
      console.log('[control-tab-manager] processedTrack:', processedTrack);
      console.log('[control-tab-manager] processedTrack type:', typeof processedTrack);
      console.log(
        '[control-tab-manager] processedTrack constructor:',
        processedTrack?.constructor?.name,
      );
      console.log(
        '[control-tab-manager] processedTrack instanceof MediaStreamTrack:',
        processedTrack instanceof MediaStreamTrack,
      );
      console.log('[control-tab-manager] processedTrack.kind:', processedTrack?.kind);
      console.log('[control-tab-manager] processedTrack.readyState:', processedTrack?.readyState);

      const allTracks = [processedTrack, ...audioTracks].filter(
        (track): track is MediaStreamTrack => {
          const isValid = track instanceof MediaStreamTrack;
          console.log(`[control-tab-manager] Track ${track?.kind} instanceof check:`, isValid);
          return isValid;
        },
      );

      console.log('[control-tab-manager] Filtered tracks count:', allTracks.length);
      console.log(
        '[control-tab-manager] All tracks:',
        allTracks.map((t) => ({
          kind: t.kind,
          constructor: t.constructor.name,
        })),
      );

      const processedStream = new MediaStream(allTracks);

      return processedStream;
    } catch (error) {
      console.error(
        '[control-tab-manager] Error processing stream through interceptor pipeline for client:',
        webClientId,
        error,
      );
      return stream; // Fallback to original stream
    }
  }

  async connectToSignalingServer() {
    try {
      console.log('[control-tab-manager] Connecting to signaling server...');
      this.websocket = new WebSocket(this.signalingServerUrl);

      this.websocket.onopen = () => {
        console.log('[control-tab-manager] Control tab connected to signaling server');
        this.isConnected = true;

        // Register as control tab
        this.sendMessage({
          type: 'register-control-tab',
          metadata: {
            userAgent: navigator.userAgent,
            timestamp: Date.now(),
          },
        });
      };

      this.websocket.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          this.handleMessage(message);
        } catch (error) {
          console.error('[control-tab-manager] Failed to parse message:', error);
        }
      };

      this.websocket.onclose = () => {
        console.log('[control-tab-manager] Control tab disconnected from signaling server');
        this.isConnected = false;
        this.scheduleReconnect();
      };

      this.websocket.onerror = (error) => {
        console.error('[control-tab-manager] Control tab WebSocket error:', error);
      };
    } catch (error) {
      console.error('[control-tab-manager] Failed to connect to signaling server:', error);
      this.scheduleReconnect();
    }
  }

  scheduleReconnect() {
    console.log('[control-tab-manager] Scheduling reconnection in 5 seconds...');
    setTimeout(() => {
      this.connectToSignalingServer();
    }, 5000);
  }

  sendMessage(message: any): void {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify(message));
    } else {
      console.warn('[control-tab-manager] Cannot send message - not connected');
    }
  }

  handleMessage(message: any): void {
    console.log('[control-tab-manager] Control tab received message:', message.type);

    switch (message.type) {
      case 'target-tabs-list':
        this.handleTargetTabsList(message);
        break;
      case 'target-tab-registered':
        this.handleTargetTabRegistered(message);
        break;
      case 'target-tab-disconnected':
        this.handleTargetTabDisconnected(message);
        break;
      case 'stream-stopped':
        this.handleStreamStopped(message);
        break;
      case 'webrtc-offer':
        this.handleWebRTCOffer();
        break;
      case 'webrtc-answer':
        this.handleWebRTCAnswer(message);
        break;
      case 'webrtc-ice-candidate':
        this.handleWebRTCIceCandidate(message);
        break;
      case 'webrtc-offer-from-target':
        this.handleTargetTabOffer(message);
        break;
      case 'webrtc-ice-candidate-from-target':
        this.handleTargetTabIceCandidate(message);
        break;
      case 'webrtc-answer-from-web-client':
        this.handleWebClientAnswer(message);
        break;
      case 'webrtc-ice-candidate-from-web-client':
        this.handleWebClientIceCandidate(message);
        break;
      case 'web-client-registered':
        this.handleWebClientRegistered(message).catch((error) => {
          console.error('[control-tab-manager] Error handling web client registration:', error);
        });
        break;
      case 'web-client-disconnected':
        this.handleWebClientDisconnected(message);
        break;
      default:
        console.log('[control-tab-manager] Unknown message type:', message.type);
    }
  }

  handleTargetTabsList(message: any): void {
    console.log('[control-tab-manager] Received target tabs list:', message.targetTabs.length);

    // Update target tabs using grouped structure
    this.tabGroups.clear();
    message.targetTabs.forEach((tab: any) => {
      this.tabGroups.set(tab.tabId, {
        tabInfo: tab,
        stream: null,
      });
    });
  }

  handleTargetTabRegistered(message: any): void {
    console.log('[control-tab-manager] Target tab registered:', message.tabId);
    this.tabGroups.set(message.tabId, {
      tabInfo: message,
      stream: null,
    });
  }

  handleTargetTabDisconnected(message: any): void {
    console.log('[control-tab-manager] Target tab disconnected:', message.tabId);

    // Clean up any active streams for this tab
    const tabGroup = this.tabGroups.get(message.tabId);
    if (tabGroup?.stream) {
      this.cleanupStream(message.tabId);
    }

    this.tabGroups.delete(message.tabId);
  }

  async handleWebClientRegistered(message: any): Promise<void> {
    console.log(
      '[control-tab-manager] Web client registered:',
      message.webClientId,
      'with purpose:',
      message.purpose,
    );

    // Initialize grouped structure for this web client
    this.webClientGroups.set(message.webClientId, {
      RTCPeerConnection: null,
      interceptorPipeline: null,
      dataChannel: null,
      webClient: {
        clientInfo: message.metadata || {},
        purpose: message.purpose || 'screen-stream',
        currentTabId: null,
        connected: true,
      },
    });

    // Configure interceptors based on client purpose
    this.configureInterceptorsForClient(message.webClientId, message.purpose);

    // Create dedicated peer connection for this web client
    await this.createPeerConnectionForWebClient(message.webClientId);
  }

  /**
   * Configure interceptors based on client purpose
   */
  configureInterceptorsForClient(webClientId: string, purpose: string): void {
    console.log(
      `[control-tab-manager] Configuring interceptors for client ${webClientId} with purpose: ${purpose}`,
    );

    let interceptorNames: string[] = [];
    let configs: Record<string, any> = {};

    switch (purpose) {
      case 'captcha':
        // Configure for captcha detection
        interceptorNames = ['video-crop', 'change-detector'];
        configs = {
          'video-crop': {
            enabled: true,
            enableCropping: true,
            cropRegion: {
              x: 0,
              y: 0,
              width: window.innerWidth / 2,
              height: window.innerHeight / 2,
            },
          },
          'change-detector': {
            enabled: false,
            changeThreshold: 5,
            stabilityThreshold: 1,
            consecutiveStableFrames: 3,
            maxWaitDuration: 5000,
            comparisonInterval: 100,
            pixelSampling: 2,
          },
        };
        break;

      case 'screen-stream':
      default:
        // Configure for basic screen streaming
        interceptorNames = [];
        configs = {};
        break;
    }

    // Set the interceptor configuration for this client
    try {
      this.interceptorRegistry.setClientConfiguration(webClientId, interceptorNames, configs);
      console.log(
        `[control-tab-manager] Interceptor configuration set for client ${webClientId}:`,
        interceptorNames,
      );
    } catch (error) {
      console.error(
        `[control-tab-manager] Failed to configure interceptors for client ${webClientId}:`,
        error,
      );
    }
  }

  handleWebClientDisconnected(message: any): void {
    console.log('[control-tab-manager] Web client disconnected:', message.webClientId);

    // Clean up peer connection and resources for this client
    this.cleanupWebClient(message.webClientId);
  }

  handleStreamStopped(message: any): void {
    console.log('[control-tab-manager] Stream stopped for tab:', message.targetTabId);
    this.cleanupStream(message.targetTabId);
  }

  async handleWebRTCOffer(): Promise<void> {
    console.log('[control-tab-manager] Received WebRTC offer');
    // This would be handled if control tab receives offers (not typical in this architecture)
  }

  async handleWebRTCAnswer(message: any): Promise<void> {
    console.log(
      '[control-tab-manager] Received WebRTC answer for web client:',
      message.webClientId,
    );

    const webClientGroup = this.webClientGroups.get(message.webClientId);
    const peerConnection = webClientGroup?.RTCPeerConnection;
    if (peerConnection) {
      try {
        await peerConnection.setRemoteDescription(new RTCSessionDescription(message.answer));
        console.log(
          '[control-tab-manager] WebRTC connection established for web client:',
          message.webClientId,
        );
      } catch (error) {
        console.error('[control-tab-manager] Failed to set remote description:', error);
      }
    }
  }

  async handleWebRTCIceCandidate(message: any): Promise<void> {
    const webClientGroup = this.webClientGroups.get(message.webClientId);
    const peerConnection = webClientGroup?.RTCPeerConnection;
    if (peerConnection) {
      try {
        await peerConnection.addIceCandidate(message.candidate);
      } catch (error) {
        console.error('[control-tab-manager] Failed to add ICE candidate:', error);
      }
    }
  }

  async handleTargetTabOffer(message: any): Promise<void> {
    console.log('[control-tab-manager] Received WebRTC offer from target tab:', message);

    // Create stream info from the message since target tab is initiating
    const streamInfo: StreamInfo = {
      targetTabId: message.targetTabId,
      status: 'connecting',
    };

    // Create peer connection to target tab
    const targetPeerConnection = new RTCPeerConnection(this.rtcConfig);

    // Store target connection using tabId as key
    this.targetConnections.set(message.targetTabId, targetPeerConnection);

    // Handle incoming stream from target tab
    targetPeerConnection.ontrack = async (event) => {
      console.log('[control-tab-manager] Target stream event:', event);
      const [stream] = event.streams;

      // Store the stream in the grouped tab structure
      const tabGroup = this.tabGroups.get(message.targetTabId);
      if (tabGroup) {
        tabGroup.stream = stream;
      }

      console.log(`[control-tab-manager] Stored active stream for tab ${message.targetTabId}`);

      // Establish CDP connection to target tab for user event handling
      try {
        console.log(
          `[control-tab-manager] Attempting to establish CDP connection for tab: ${message.targetTabId}`,
        );
        console.log(`[control-tab-manager] CDP Manager available:`, !!this.cdpManager);

        if (this.cdpManager) {
          console.log(
            `[control-tab-manager] Adding CDP connection for tab: ${message.targetTabId}`,
          );

          console.log(
            `[control-tab-manager] Creating CDP target info for tab: ${message.targetTabId}`,
          );

          await (this.cdpManager as any).addConnection(message.targetTabId);

          console.log(
            `[control-tab-manager] CDP connection established for tab: ${message.targetTabId}`,
          );
        } else {
          console.warn(
            '[control-tab-manager] CDP Manager not available - user events will not work',
          );
        }
      } catch (error) {
        console.error(
          `[control-tab-manager] Failed to establish CDP connection for tab ${message.targetTabId}:`,
          error,
        );
      }

      // Display the original stream in control tab (no processing needed here)
      this.displayStreamInControlTab(message.targetTabId, stream, streamInfo);

      // Broadcast original stream to ALL connected web clients (each will process individually)
      this.broadcastStreamToAllClients(stream, message.targetTabId)
    };

    // Handle ICE candidates from target tab
    targetPeerConnection.onicecandidate = (event) => {
      if (event.candidate) {
        // Send ICE candidate back to target tab
        this.sendMessage({
          type: 'webrtc-ice-candidate-to-target',
          candidate: event.candidate,
          targetTabId: streamInfo.targetTabId,
        });
      }
    };

    // Accept the offer from target tab
    try {
      await targetPeerConnection.setRemoteDescription(new RTCSessionDescription(message.offer));
      const answer = await targetPeerConnection.createAnswer();
      await targetPeerConnection.setLocalDescription(answer);

      // Send answer back to target tab
      this.sendMessage({
        type: 'webrtc-answer-to-target',
        answer: answer,
        targetTabId: streamInfo.targetTabId,
      });

      console.log('[control-tab-manager] WebRTC answer sent to target tab');
    } catch (error) {
      console.error('[control-tab-manager] Failed to handle target tab offer:', error);
    }
  }

  async handleTargetTabIceCandidate(message: any): Promise<void> {
    const targetPeerConnection = this.targetConnections.get(message.targetTabId);
    if (targetPeerConnection) {
      try {
        await targetPeerConnection.addIceCandidate(message.candidate);
      } catch (error) {
        console.error('[control-tab-manager] Failed to add target ICE candidate:', error);
      }
    }
  }

  async handleWebClientAnswer(message: any): Promise<void> {
    console.log(
      '[control-tab-manager] Received WebRTC answer from web client:',
      message.webClientId,
    );

    const webClientGroup = this.webClientGroups.get(message.webClientId);
    const peerConnection = webClientGroup?.RTCPeerConnection;
    if (peerConnection) {
      try {
        await peerConnection.setRemoteDescription(new RTCSessionDescription(message.answer));
        console.log(
          '[control-tab-manager] WebRTC connection established with web client:',
          message.webClientId,
        );
      } catch (error) {
        console.error(
          '[control-tab-manager] Failed to set remote description from web client:',
          error,
        );
      }
    } else {
      console.warn(
        '[control-tab-manager] No peer connection found for web client:',
        message.webClientId,
      );
    }
  }

  async handleWebClientIceCandidate(message: any): Promise<void> {
    console.log(
      '[control-tab-manager] Received ICE candidate from web client:',
      message.webClientId,
    );

    const webClientGroup = this.webClientGroups.get(message.webClientId);
    const peerConnection = webClientGroup?.RTCPeerConnection;
    if (peerConnection) {
      try {
        await peerConnection.addIceCandidate(message.candidate);
      } catch (error) {
        console.error('[control-tab-manager] Failed to add ICE candidate from web client:', error);
      }
    } else {
      console.warn(
        '[control-tab-manager] No peer connection found for web client:',
        message.webClientId,
      );
    }
  }

  async createPeerConnectionForWebClient(webClientId: string): Promise<void> {
    console.log('[control-tab-manager] Creating peer connection for web client:', webClientId);

    const peerConnection = new RTCPeerConnection(this.rtcConfig);

    // Create data channel for user events
    const dataChannel = peerConnection.createDataChannel('userEvents', {
      ordered: true,
    });
    this.setupDataChannelHandlers(dataChannel, webClientId);

    // Handle ICE candidates from web client
    peerConnection.onicecandidate = (event) => {
      if (event.candidate) {
        this.sendMessage({
          type: 'webrtc-ice-candidate-to-web-client',
          candidate: event.candidate,
          webClientId: webClientId,
        });
      }
    };

    // Store the peer connection and data channel in the grouped structure
    const webClientGroup = this.webClientGroups.get(webClientId);
    if (webClientGroup) {
      webClientGroup.RTCPeerConnection = peerConnection;
      webClientGroup.dataChannel = dataChannel;
    }

    // Check for existing active streams and add tracks immediately
    await this.addExistingTracksToNewClient(webClientId, peerConnection);

    console.log('[control-tab-manager] Peer connection created for web client:', webClientId);
  }

  /**
   * Add existing active tracks to a newly connected web client
   */
  async addExistingTracksToNewClient(
    webClientId: string,
    peerConnection: RTCPeerConnection,
  ): Promise<void> {
    console.log(
      '[control-tab-manager] Checking for existing active streams for new client:',
      webClientId,
    );

    // Check all tab groups for active streams
    for (const [tabId, tabGroup] of this.tabGroups) {
      if (tabGroup.stream) {
        console.log(
          `[control-tab-manager] Found active stream for tab ${tabId}, adding tracks to new client ${webClientId}`,
        );

        // Get the stored stream
        const originalStream = tabGroup.stream;
        if (originalStream && originalStream.getTracks().length > 0) {
          // Process the stream for this specific client
          const clientProcessedStream = await this.processStreamForClient(
            originalStream,
            webClientId,
          );

          // Update client's current tab
          const webClientGroup = this.webClientGroups.get(webClientId);
          if (webClientGroup?.webClient) {
            webClientGroup.webClient.currentTabId = tabId;
          }

          // Add tracks to the peer connection
          clientProcessedStream.getTracks().forEach((track) => {
            console.log(
              `[control-tab-manager] Adding existing ${track.kind} track to new client:`,
              webClientId,
            );
            peerConnection.addTrack(track, clientProcessedStream);
          });

          this.createOfferToWebClient(tabId, webClientId);

          console.log(
            `[control-tab-manager] Added existing stream tracks from tab ${tabId} to new client ${webClientId}`,
          );

          // Only add tracks from the first active stream to avoid conflicts
          break;
        }
      }
    }

    // Check if no active streams were found
    const hasActiveStreams = Array.from(this.tabGroups.values()).some(
      (tabGroup) => tabGroup.stream,
    );
    if (!hasActiveStreams) {
      console.log(
        '[control-tab-manager] No existing active streams found for new client:',
        webClientId,
      );
    }
  }

  cleanupWebClient(webClientId: string): void {
    console.log('[control-tab-manager] Cleaning up web client:', webClientId);

    // Get the grouped structure for this client
    const webClientGroup = this.webClientGroups.get(webClientId);

    if (webClientGroup) {
      // Close and remove peer connection
      if (webClientGroup.RTCPeerConnection) {
        webClientGroup.RTCPeerConnection.close();
      }

      // Clean up data channel
      if (webClientGroup.dataChannel) {
        webClientGroup.dataChannel.close();
      }
    }

    // Remove from web client groups map
    this.webClientGroups.delete(webClientId);

    console.log('[control-tab-manager] Web client cleanup completed:', webClientId);
  }

  async broadcastStreamToAllClients(stream: MediaStream, targetTabId: string): Promise<void> {
    console.log(
      '[control-tab-manager] Broadcasting stream to all connected web clients for tab:',
      targetTabId,
      'for webClients',
      this.webClientGroups,
    );

    // Iterate through all connected web clients using grouped structure
    for (const [webClientId, webClientGroup] of this.webClientGroups) {
      const peerConnection = webClientGroup.RTCPeerConnection;
      if (!peerConnection) continue;

      console.log(
        '[control-tab-manager] Processing web client:',
        webClientId,
        '- Connection state:',
        peerConnection.connectionState,
      );

      // Update client's current tab
      if (webClientGroup.webClient) {
        webClientGroup.webClient.currentTabId = targetTabId;
      }

      // Process stream individually for this client
      const clientProcessedStream = await this.processStreamForClient(stream, webClientId);

      // Add or replace tracks for this client with their processed stream
      clientProcessedStream.getTracks().forEach((track) => {
        const sender = peerConnection
          .getSenders()
          .find((s) => s.track && s.track.kind === track.kind);

        if (sender) {
          console.log(
            `[control-tab-manager] Replacing ${track.kind} track for client:`,
            webClientId,
          );
          sender.replaceTrack(track);
        } else {
          console.log(`[control-tab-manager] Adding ${track.kind} track for client:`, webClientId);
          peerConnection.addTrack(track, clientProcessedStream);
        }
      });

      // Check connection state before creating new offer
      const connectionState = peerConnection.connectionState;
      const signalingState = peerConnection.signalingState;

      if (connectionState === 'connected') {
        console.log(
          `[control-tab-manager] Client ${webClientId} already connected - tracks updated via existing connection`,
        );
      } else if (
        signalingState === 'stable' &&
        (connectionState === 'new' || connectionState === 'connecting')
      ) {
        console.log(
          `[control-tab-manager] Creating new offer for client ${webClientId} (connection: ${connectionState}, signaling: ${signalingState})`,
        );
        this.createOfferToWebClient(targetTabId, webClientId);
      } else if (signalingState !== 'stable') {
        console.log(
          `[control-tab-manager] Skipping offer for client ${webClientId} - negotiation in progress (signaling: ${signalingState})`,
        );
      } else {
        console.log(
          `[control-tab-manager] Creating offer for client ${webClientId} (connection: ${connectionState}, signaling: ${signalingState})`,
        );
        this.createOfferToWebClient(targetTabId, webClientId);
      }
    }

    console.log(
      '[control-tab-manager] Stream broadcast completed to',
      this.webClientGroups.size,
      'clients',
    );
  }

  async createOfferToWebClient(tabId: string, webClientId: string): Promise<void> {
    const webClientGroup = this.webClientGroups.get(webClientId);
    const peerConnection = webClientGroup?.RTCPeerConnection;
    if (!peerConnection) {
      console.error('[control-tab-manager] No peer connection found for web client:', webClientId);
      return;
    }

    // Double-check connection state before creating offer
    const connectionState = peerConnection.connectionState;
    const signalingState = peerConnection.signalingState;

    console.log(
      `[control-tab-manager] Creating offer for client ${webClientId} - Connection: ${connectionState}, Signaling: ${signalingState}`,
    );

    try {
      const offer = await peerConnection.createOffer();
      await peerConnection.setLocalDescription(offer);

      // Send offer to web client
      this.sendMessage({
        type: 'webrtc-offer-to-web-client',
        offer: offer,
        targetClientId: webClientId,
        tabId: tabId,
        fromClientId: this.clientId,
      });

      console.log(
        `[control-tab-manager] WebRTC offer sent to web client: ${webClientId} (${connectionState} -> negotiating)`,
      );
    } catch (error) {
      console.error(
        `[control-tab-manager] Failed to create offer to web client ${webClientId}:`,
        (error as Error).message,
      );

      // Log additional context for debugging
      console.error(
        `[control-tab-manager] Error context - Connection: ${connectionState}, Signaling: ${signalingState}`,
      );
    }
  }

  cleanupStream(tabId: string): void {
    console.log('[control-tab-manager] Cleaning up stream for tab:', tabId);

    // Clean up target connection
    const targetConnection = this.targetConnections.get(tabId);
    if (targetConnection) {
      targetConnection.close();
      this.targetConnections.delete(tabId);
    }

    // Update all web clients to remove tracks from this tab
    for (const [webClientId, webClientGroup] of this.webClientGroups) {
      if (webClientGroup.webClient?.currentTabId === tabId) {
        webClientGroup.webClient.currentTabId = null;

        // Remove tracks from this client's peer connection
        const peerConnection = webClientGroup.RTCPeerConnection;
        if (peerConnection) {
          peerConnection.getSenders().forEach((sender) => {
            if (sender.track) {
              peerConnection.removeTrack(sender);
            }
          });
        }
      }
    }

    // Remove from UI
    const streamElement = document.getElementById(`poc-stream-${tabId}`);
    if (streamElement) {
      streamElement.remove();
    }

    // Add "no streams" message if no streams left
    const streamsContainer = document.getElementById('poc-streams-container');
    if (streamsContainer && streamsContainer.children.length === 0) {
      streamsContainer.innerHTML =
        '<div style="color: #666; font-style: italic;">No active streams</div>';
    }

    // Clear stream from tab group
    const tabGroup = this.tabGroups.get(tabId);
    if (tabGroup) {
      tabGroup.stream = null;
    }

    try {
      if (this.cdpManager) {
        (this.cdpManager as any).removeConnection(tabId);
        console.log(`[control-tab-manager] CDP connection cleaned up for tab: ${tabId}`);
      }
    } catch (error) {
      console.error(
        `[control-tab-manager] Failed to cleanup CDP connection for tab ${tabId}:`,
        error,
      );
    }
  }

  createControlTabUI(): void {
    // Create a floating control panel for the control tab
    const controlPanel = document.createElement('div');
    controlPanel.id = 'poc-control-panel';
    controlPanel.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        width: 400px;
        max-height: 600px;
        background: rgba(0, 0, 0, 0.9);
        color: white;
        border-radius: 8px;
        padding: 16px;
        z-index: 10000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        font-size: 14px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        overflow-y: auto;
      `;

    controlPanel.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px;">
          <h3 style="margin: 0; color: #4CAF50;">POC Control Tab</h3>
          <button id="poc-toggle-panel" style="background: none; border: 1px solid #666; color: white; padding: 4px 8px; border-radius: 4px; cursor: pointer;">−</button>
        </div>
        <div id="poc-panel-content">
          <div style="margin-bottom: 12px;">
            <div style="font-weight: bold; margin-bottom: 4px;">Status:</div>
            <div id="poc-connection-status" style="color: #ff9800;">Connecting...</div>
          </div>
          <div style="margin-bottom: 12px;">
            <div style="font-weight: bold; margin-bottom: 4px;">Active Streams:</div>
            <div id="poc-streams-container" style="max-height: 300px; overflow-y: auto;">
              <div style="color: #666; font-style: italic;">No active streams</div>
            </div>
          </div>
          <div style="margin-bottom: 12px;">
            <div style="font-weight: bold; margin-bottom: 4px;">Target Tabs:</div>
            <div id="poc-tabs-container" style="max-height: 200px; overflow-y: auto;">
              <div style="color: #666; font-style: italic;">No target tabs</div>
            </div>
          </div>
          <div style="margin-bottom: 12px;">
            <div style="font-weight: bold; margin-bottom: 4px;">Interceptor Debug Panel:</div>
            <div id="poc-interceptor-debug-container" style="max-height: 400px; overflow-y: auto; border: 1px solid #444; border-radius: 4px; padding: 8px; background: rgba(255,255,255,0.05);">
              <div style="color: #666; font-style: italic;">No interceptor data</div>
            </div>
            <button id="poc-refresh-interceptors" style="margin-top: 8px; background: #4CAF50; border: none; color: white; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 12px;">Refresh Interceptor Status</button>
          </div>
        </div>
      `;

    document.body.appendChild(controlPanel);

    // Add toggle functionality
    const toggleBtn = document.getElementById('poc-toggle-panel');
    const panelContent = document.getElementById('poc-panel-content');
    let isCollapsed = false;

    toggleBtn?.addEventListener('click', () => {
      isCollapsed = !isCollapsed;
      if (panelContent) {
        panelContent.style.display = isCollapsed ? 'none' : 'block';
      }
      if (toggleBtn) {
        toggleBtn.textContent = isCollapsed ? '+' : '−';
      }
      controlPanel.style.height = isCollapsed ? 'auto' : '';
    });

    // Add refresh interceptors button functionality
    const refreshBtn = document.getElementById('poc-refresh-interceptors');
    if (refreshBtn) {
      refreshBtn.addEventListener('click', () => {
        this.updateInterceptorDebugPanel();
      });
    }

    // Update connection status
    this.updateConnectionStatus('Connected');

    // Initial interceptor debug update
    this.updateInterceptorDebugPanel();
  }

  updateConnectionStatus(status: string): void {
    const statusElement = document.getElementById('poc-connection-status');
    if (statusElement) {
      statusElement.textContent = status;
      statusElement.style.color = status === 'Connected' ? '#4CAF50' : '#ff9800';
    }
  }

  displayStreamInControlTab(tabId: string, mediaStream: MediaStream, streamInfo: StreamInfo): void {
    console.log('[control-tab-manager] Displaying stream in control tab for tab:', tabId);

    const streamsContainer = document.getElementById('poc-streams-container');
    if (!streamsContainer) {
      console.warn('[control-tab-manager] Streams container not found');
      return;
    }

    // Remove "no streams" message if present
    const noStreamsMsg = streamsContainer.querySelector('[style*="font-style: italic"]');
    if (noStreamsMsg && noStreamsMsg.textContent?.includes('No active streams')) {
      noStreamsMsg.remove();
    }

    // Create stream display element
    const streamElement = document.createElement('div');
    streamElement.id = `poc-stream-${tabId}`;
    streamElement.style.cssText = `
        margin-bottom: 12px;
        padding: 8px;
        border: 1px solid #333;
        border-radius: 4px;
        background: rgba(255, 255, 255, 0.05);
      `;

    streamElement.innerHTML = `
        <div style="font-weight: bold; margin-bottom: 8px; color: #4CAF50;">
          📺 Tab ${tabId.substring(0, 8)}...
        </div>
        <video
          id="poc-video-${tabId}"
          autoplay
          muted
          playsinline
          style="width: 100%; height: 150px; background: #000; border-radius: 4px; object-fit: contain;"
        ></video>
        <div style="margin-top: 8px; font-size: 12px; color: #ccc;">
          Target: ${streamInfo.targetTabId || 'Unknown'}
        </div>
      `;

    streamsContainer.appendChild(streamElement);

    // Set the video source
    const video = document.getElementById(`poc-video-${tabId}`) as HTMLVideoElement;
    if (video && mediaStream) {
      video.srcObject = mediaStream;

      video.onloadedmetadata = () => {
        console.log('[control-tab-manager] Video metadata loaded in control tab');
      };

      video.onplay = () => {
        console.log('[control-tab-manager] Video started playing in control tab');
      };

      video.onerror = (error) => {
        console.error('[control-tab-manager] Video error in control tab:', error);
      };
    }
  }

  setupPageListeners(): void {
    // Listen for page unload
    window.addEventListener('beforeunload', () => {
      console.log('[control-tab-manager] Control tab unloading...');

      // Clean up all streams
      for (const [tabId, tabGroup] of this.tabGroups) {
        if (tabGroup.stream) {
          this.cleanupStream(tabId);
        }
      }

      // Close WebSocket
      if (this.websocket) {
        this.websocket.close();
      }
    });
  }

  /**
   * Setup data channel handlers for user event replay
   */
  setupDataChannelHandlers(dataChannel: RTCDataChannel, webClientId: string): void {
    console.log('[control-tab-manager] Setting up data channel for web client:', webClientId);

    dataChannel.onopen = () => {
      console.log('[control-tab-manager] Data channel opened for web client:', webClientId);
    };

    dataChannel.onclose = () => {
      console.log('[control-tab-manager] Data channel closed for web client:', webClientId);
    };

    dataChannel.onerror = (error) => {
      console.error('[control-tab-manager] Data channel error:', error);
    };

    dataChannel.onmessage = (event) => {
      try {
        const userEvent = JSON.parse(event.data);
        console.log('[control-tab-manager] Received user event:', userEvent);

        // Get the current tab this client is viewing from grouped structure
        const webClientGroup = this.webClientGroups.get(webClientId);
        const targetTabId = webClientGroup?.webClient?.currentTabId;

        console.log('[control-tab-manager] Web client group:', webClientGroup);
        console.log('[control-tab-manager] Target tab ID:', targetTabId);
        console.log('[control-tab-manager] CDP Manager available:', !!this.cdpManager);

        if (targetTabId) {
          console.log('[control-tab-manager] Handling user event for target tab:', targetTabId);
          this.handleUserEvent(webClientId, userEvent, targetTabId);
        } else {
          console.warn(
            '[control-tab-manager] No target tab for user event from client:',
            webClientId,
          );
        }
      } catch (error) {
        console.error('[control-tab-manager] Failed to parse user event:', error);
      }
    };
  }

  /**
   * Handle user events from web client
   */
  async handleUserEvent(
    webClientId: string,
    userEvent: UserEvent,
    targetTabId: string,
  ): Promise<void> {
    if (userEvent.type !== 'user-event') {
      console.warn('[control-tab-manager] Unknown event type:', userEvent.type);
      return;
    }

    if (userEvent.eventType === 'click') {
      // Enable Change detector interceptor when click is detected
      this.enableChangeDetectorForClient(webClientId);
    }

    try {
      await this.replayEventOnTargetTab(userEvent, targetTabId);
    } catch (error) {
      console.error('[control-tab-manager] Failed to replay event:', error);
    }
  }

  /**
   * Enable Change detector interceptor for a specific client when click is detected
   */
  enableChangeDetectorForClient(webClientId: string): void {
    console.log(
      '[control-tab-manager] Enabling Change detector for client:',
      webClientId,
      'due to click event',
    );

    // Get the web client group for this client
    const webClientGroup = this.webClientGroups.get(webClientId);
    if (!webClientGroup) {
      console.warn('[control-tab-manager] No web client group found for:', webClientId);
      return;
    }

    // Get the interceptor pipeline for this client
    const interceptorPipeline = webClientGroup.interceptorPipeline;
    if (!interceptorPipeline) {
      console.warn('[control-tab-manager] No interceptor pipeline found for client:', webClientId);
      return;
    }

    // Get the Change detector interceptor
    const changeDetectorInterceptor = interceptorPipeline.getInterceptor('change-detector');

    if (changeDetectorInterceptor) {
      // Set up WebSocket messaging for the interceptor
      (changeDetectorInterceptor as any).setControlTabManager(this);
      (changeDetectorInterceptor as any).setTriggeringWebClient(webClientId);

      // Simply enable the interceptor - it will start monitoring automatically
      (changeDetectorInterceptor as any).updateConfig({ enabled: true });

      console.log(
        `[control-tab-manager] Change detector interceptor enabled for client ${webClientId}`,
      );
    } else {
      console.warn(
        `[control-tab-manager] Change detector interceptor not found for client ${webClientId}`,
      );
    }
  }

  /**
   * Replay user event on target tab using CDPManager
   */
  async replayEventOnTargetTab(userEvent: UserEvent, targetTabId: string): Promise<void> {
    if (!this.cdpManager) {
      console.warn('[control-tab-manager] CDP Manager not available - cannot replay event');
      return;
    }

    try {
      // Use CDPManager to handle the user event
      await (this.cdpManager as any).handleUserEvent(userEvent, targetTabId);
    } catch (error) {
      console.error('[control-tab-manager] Failed to replay event via CDPManager:', error);
      throw error;
    }
  }

  /**
   * Toggle interceptor for a target tab
   */
  toggleInterceptor(tabId: string): void {
    console.log(
      '[control-tab-manager] Interceptor controls are now per-client. Use the web client interface.',
    );
  }

  /**
   * Toggle cropping for a target tab
   */
  toggleCropping(tabId: string): void {
    console.log(
      '[control-tab-manager] Cropping controls are now per-client. Use the web client interface.',
    );
  }

  /**
   * Set crop region for a target tab
   */
  setCropRegion(tabId: string): void {
    console.log(
      '[control-tab-manager] Crop region controls are now per-client. Use the web client interface.',
    );
  }

  /**
   * Update the interceptor debug panel with current status
   */
  updateInterceptorDebugPanel(): void {
    const container = document.getElementById('poc-interceptor-debug-container');
    if (!container) return;

    try {
      let debugHtml = '';

      // Registry Status
      debugHtml += `<div style="margin-bottom: 12px; padding: 8px; background: rgba(0,100,200,0.1); border-radius: 4px;">`;
      debugHtml += `<div style="font-weight: bold; color: #64B5F6; margin-bottom: 4px;">📋 Interceptor Registry</div>`;

      if (typeof this.interceptorRegistry !== 'undefined') {
        const registeredClasses = Array.from(
          (this.interceptorRegistry as any).interceptorClasses.keys(),
        );
        debugHtml += `<div style="font-size: 12px;">Registered: ${
          registeredClasses.join(', ') || 'None'
        }</div>`;
        debugHtml += `<div style="font-size: 12px;">Total Clients: ${
          (this.interceptorRegistry as any).clientInterceptors.size
        }</div>`;
      } else {
        debugHtml += `<div style="color: #ff9800; font-size: 12px;">Registry not available</div>`;
      }
      debugHtml += `</div>`;

      // Web Client Groups
      debugHtml += `<div style="margin-bottom: 12px; padding: 8px; background: rgba(100,200,0,0.1); border-radius: 4px;">`;
      debugHtml += `<div style="font-weight: bold; color: #81C784; margin-bottom: 4px;">🌐 Web Client Groups (${this.webClientGroups.size})</div>`;

      if (this.webClientGroups.size === 0) {
        debugHtml += `<div style="color: #666; font-size: 12px; font-style: italic;">No active web clients</div>`;
      } else {
        for (const [webClientId, group] of this.webClientGroups) {
          debugHtml += `<div style="margin-bottom: 8px; padding: 6px; background: rgba(255,255,255,0.05); border-radius: 3px;">`;
          debugHtml += `<div style="font-weight: bold; font-size: 12px; color: #81C784;">Client: ${webClientId.substring(
            0,
            8,
          )}...</div>`;

          // Pipeline Info
          if (group.interceptorPipeline) {
            const pipeline = group.interceptorPipeline;
            debugHtml += `<div style="font-size: 11px; margin-top: 4px;">`;
            debugHtml += `<span style="color: #4CAF50;">✓ Pipeline Active</span> | `;
            debugHtml += `Interceptors: ${pipeline.interceptors?.length || 0}`;
            debugHtml += `</div>`;

            // Individual Interceptors
            if (pipeline.interceptors && pipeline.interceptors.length > 0) {
              debugHtml += `<div style="margin-top: 6px; padding-left: 8px; border-left: 2px solid #4CAF50;">`;
              for (const interceptor of pipeline.interceptors) {
                const status = interceptor.getStats ? interceptor.getStats() : {};
                debugHtml += `<div style="font-size: 10px; margin-bottom: 3px;">`;
                debugHtml += `<span style="color: #FFD54F;">🔧 ${
                  interceptor.name || 'Unknown'
                }</span><br>`;
                debugHtml += `<span style="color: #B0BEC5;">Enabled: ${
                  (status as any).config?.enabled || false
                }</span><br>`;

                // Specific interceptor details
                if (interceptor.name === 'video-crop') {
                  debugHtml += `<span style="color: #B0BEC5;">Cropping: ${
                    (status as any).enableCropping || 'Unknown'
                  }</span><br>`;
                  if ((status as any).cropRegion) {
                    debugHtml += `<span style="color: #B0BEC5;">Region: ${
                      (status as any).cropRegion.x
                    },${(status as any).cropRegion.y} ${
                      (status as any).cropRegion.width
                    }x${(status as any).cropRegion.height}</span><br>`;
                  }
                } else if (interceptor.name === 'brightness-filter') {
                  debugHtml += `<span style="color: #B0BEC5;">Brightness: ${
                    (status as any).brightness || 'Unknown'
                  }</span><br>`;
                } else if (interceptor.name === 'blur-effect') {
                  debugHtml += `<span style="color: #B0BEC5;">Blur Radius: ${
                    (status as any).blurRadius || 'Unknown'
                  }</span><br>`;
                }

                debugHtml += `<span style="color: #B0BEC5;">Frames: ${
                  (status as any).frameCount || 0
                }</span>`;
                debugHtml += `</div>`;
              }
              debugHtml += `</div>`;
            }
          } else {
            debugHtml += `<div style="font-size: 11px; margin-top: 4px; color: #ff9800;">⚠ No Pipeline</div>`;
          }

          debugHtml += `</div>`;
        }
      }
      debugHtml += `</div>`;

      // Performance Stats
      debugHtml += `<div style="padding: 8px; background: rgba(200,0,100,0.1); border-radius: 4px;">`;
      debugHtml += `<div style="font-weight: bold; color: #F48FB1; margin-bottom: 4px;">📊 Performance</div>`;
      debugHtml += `<div style="font-size: 11px; color: #B0BEC5;">`;
      debugHtml += `Active Connections: ${this.webClientGroups.size}<br>`;
      debugHtml += `Target Tabs: ${this.tabGroups.size}<br>`;
      debugHtml += `Last Update: ${new Date().toLocaleTimeString()}`;
      debugHtml += `</div>`;
      debugHtml += `</div>`;

      container.innerHTML = debugHtml;
    } catch (error) {
      container.innerHTML = `<div style="color: #f44336; font-size: 12px;">Error updating debug panel: ${
        (error as Error).message
      }</div>`;
      console.error('[control-tab-manager] Error updating interceptor debug panel:', error);
    }
  }
}

export default ControlTabManager;
